import typing
import discord
import logging
import async<PERSON>
import json
from datetime import datetime, timedelta
from collections import defaultdict, deque
from discord.ext import commands

from utilities import checks
from utilities import converters
from utilities import decorators
from utilities import helpers

logger = logging.getLogger(__name__)


async def setup(bot):
    await bot.add_cog(Antinuke(bot))


class Antinuke(commands.Cog):
    """
    Antinuke to protect your server from mass actions and raids.
    """

    def __init__(self, bot):
        self.bot = bot
        # Track recent actions for rate limiting
        self.action_tracker = defaultdict(lambda: defaultdict(deque))
        # Cache for antinuke settings
        self.settings_cache = {}
        
    async def get_antinuke_settings(self, guild_id):
        """Get antinuke settings for a guild, with caching"""
        if guild_id in self.settings_cache:
            return self.settings_cache[guild_id]
            
        query = """
                SELECT * FROM antinuke 
                WHERE server_id = $1;
                """
        settings = await self.bot.cxn.fetchrow(query, guild_id)
        
        if not settings:
            # Create default settings
            await self.create_default_settings(guild_id)
            settings = await self.bot.cxn.fetchrow(query, guild_id)
            
        self.settings_cache[guild_id] = dict(settings)
        return self.settings_cache[guild_id]
    
    async def create_default_settings(self, guild_id):
        """Create default antinuke settings for a guild"""
        query = """
                INSERT INTO antinuke (server_id)
                VALUES ($1)
                ON CONFLICT (server_id) DO NOTHING;
                """
        await self.bot.cxn.execute(query, guild_id)
    
    async def update_settings_cache(self, guild_id):
        """Update the settings cache for a guild"""
        if guild_id in self.settings_cache:
            del self.settings_cache[guild_id]
        await self.get_antinuke_settings(guild_id)
    
    def is_owner_or_admin(self, ctx, user_id=None):
        """Check if user is server owner or antinuke admin"""
        user_id = user_id or ctx.author.id
        
        # Server owner
        if ctx.guild.owner_id == user_id:
            return True
            
        # Bot owners
        if user_id in self.bot.config.OWNERS:
            return True
            
        return False
    
    async def is_antinuke_admin(self, ctx, user_id=None):
        """Check if user is an antinuke admin"""
        user_id = user_id or ctx.author.id
        
        if self.is_owner_or_admin(ctx, user_id):
            return True
            
        settings = await self.get_antinuke_settings(ctx.guild.id)
        return user_id in settings.get('admin_users', [])
    
    def is_whitelisted(self, settings, user_id):
        """Check if user/bot is whitelisted"""
        return (user_id in settings.get('whitelisted_users', []) or 
                user_id in settings.get('whitelisted_bots', []))
    
    async def log_action(self, guild_id, user_id, action_type, punishment=None, details=None):
        """Log an antinuke action"""
        query = """
                INSERT INTO antinuke_logs (server_id, user_id, action_type, punishment, details)
                VALUES ($1, $2, $3, $4, $5);
                """
        await self.bot.cxn.execute(
            query, guild_id, user_id, action_type, punishment, 
            json.dumps(details or {})
        )
    
    async def check_rate_limit(self, guild_id, user_id, action_type, threshold):
        """Check if user has exceeded rate limit for an action"""
        now = datetime.utcnow()
        minute_ago = now - timedelta(minutes=1)
        
        # Clean old entries
        user_actions = self.action_tracker[guild_id][user_id]
        while user_actions and user_actions[0] < minute_ago:
            user_actions.popleft()
        
        # Add current action
        user_actions.append(now)
        
        # Check if threshold exceeded
        return len(user_actions) > threshold
    
    async def punish_user(self, guild, user, punishment_type, reason):
        """Apply punishment to a user"""
        try:
            if punishment_type == 'kick':
                await guild.kick(user, reason=reason)
            elif punishment_type == 'ban':
                await guild.ban(user, reason=reason)
            elif punishment_type == 'strip_roles':
                # Remove all roles except @everyone
                roles_to_remove = [role for role in user.roles if role != guild.default_role]
                if roles_to_remove:
                    await user.remove_roles(*roles_to_remove, reason=reason)
            return True
        except Exception as e:
            logger.error(f"Failed to punish user {user.id} in guild {guild.id}: {e}")
            return False
    
    @decorators.group(
        name="antinuke",
        aliases=["an"],
        brief="Antinuke to protect your server",
        invoke_without_command=True
    )
    @checks.guild_only()
    async def antinuke(self, ctx):
        """
        Antinuke system to protect your server from mass actions and raids.
        
        Use subcommands to configure different protection modules.
        """
        if ctx.invoked_subcommand is None:
            settings = await self.get_antinuke_settings(ctx.guild.id)
            
            embed = discord.Embed(
                title="Antinuke Protection",
                description="Server protection against mass actions and raids",
                color=0x2596BE if settings['enabled'] else 0x808080
            )

            status = "Enabled" if settings['enabled'] else "Disabled"
            embed.add_field(name="Status", value=status, inline=True)

            # Show enabled modules
            modules = []
            module_checks = [
                ('kick_enabled', 'Member Kick'),
                ('ban_enabled', 'Member Ban'),
                ('channel_enabled', 'Channel Create/Delete'),
                ('role_enabled', 'Role Delete'),
                ('webhook_enabled', 'Webhook Creation'),
                ('emoji_enabled', 'Emoji Delete'),
                ('vanity_enabled', 'Vanity URL'),
                ('botadd_enabled', 'Bot Addition'),
                ('permissions_enabled', 'Dangerous Permissions')
            ]

            for setting, name in module_checks:
                if settings.get(setting, False):
                    modules.append(f"+ {name}")
                else:
                    modules.append(f"- {name}")
            
            embed.add_field(
                name="Protection Modules", 
                value="\n".join(modules[:5]), 
                inline=True
            )
            embed.add_field(
                name="‎", 
                value="\n".join(modules[5:]), 
                inline=True
            )
            
            embed.add_field(
                name="Whitelisted Users", 
                value=str(len(settings.get('whitelisted_users', []))), 
                inline=True
            )
            embed.add_field(
                name="Whitelisted Bots", 
                value=str(len(settings.get('whitelisted_bots', []))), 
                inline=True
            )
            embed.add_field(
                name="Admin Users", 
                value=str(len(settings.get('admin_users', []))), 
                inline=True
            )
            
            embed.set_footer(text=f"Use {ctx.prefix}antinuke help for more commands")
            await ctx.send(embed=embed)

    @antinuke.command(brief="Show all antinuke commands and their usage")
    @checks.guild_only()
    async def help(self, ctx):
        """
        Display all available antinuke commands with descriptions and usage examples.
        """
        embed = discord.Embed(
            title="Antinuke Commands Help",
            description="Complete list of antinuke protection commands",
            color=0x2596BE
        )

        # Configuration commands
        config_commands = [
            f"`{ctx.prefix}antinuke config` - View current antinuke configuration",
            f"`{ctx.prefix}antinuke admin <member>` - Add/remove antinuke admin",
            f"`{ctx.prefix}antinuke admins` - View all antinuke admins",
            f"`{ctx.prefix}antinuke whitelist <user/bot>` - Add/remove from whitelist",
            f"`{ctx.prefix}antinuke list` - View enabled modules and whitelisted users"
        ]

        # Protection modules
        protection_commands = [
            f"`{ctx.prefix}antinuke kick <true/false> [threshold] [punishment]` - Mass kick protection",
            f"`{ctx.prefix}antinuke ban <true/false> [threshold] [punishment]` - Mass ban protection",
            f"`{ctx.prefix}antinuke channel <true/false> [threshold] [punishment]` - Channel spam protection",
            f"`{ctx.prefix}antinuke role <true/false> [threshold] [punishment]` - Role delete protection",
            f"`{ctx.prefix}antinuke webhook <true/false> [threshold] [punishment]` - Webhook spam protection",
            f"`{ctx.prefix}antinuke emoji <true/false> [threshold] [punishment]` - Emoji delete protection",
            f"`{ctx.prefix}antinuke vanity <true/false> [punishment]` - Vanity URL protection",
            f"`{ctx.prefix}antinuke botadd <true/false> [punishment]` - Bot addition protection",
            f"`{ctx.prefix}antinuke permissions [type] [permission]` - Dangerous permissions protection"
        ]

        embed.add_field(
            name="Configuration Commands",
            value="\n".join(config_commands),
            inline=False
        )

        embed.add_field(
            name="Protection Modules",
            value="\n".join(protection_commands),
            inline=False
        )

        embed.add_field(
            name="Usage Tips",
            value="- Use `true/false`, `yes/no`, `on/off`, or `enable/disable` for status\n"
                  "- Threshold = actions per minute before triggering\n"
                  "- Punishment options: `kick`, `ban`, `mute`\n"
                  "- Whitelisted users bypass all protections",
            inline=False
        )

        embed.set_footer(text=f"Example: {ctx.prefix}antinuke kick true 5 ban")
        await ctx.send(embed=embed)

    @antinuke.command(brief="View server configuration for Antinuke")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def config(self, ctx):
        """
        View detailed antinuke configuration for the server.
        """
        settings = await self.get_antinuke_settings(ctx.guild.id)
        
        embed = discord.Embed(
            title="Antinuke Configuration",
            color=0x2596BE
        )

        # Basic settings
        log_channel = settings.get('log_channel')
        log_channel_text = f'<#{log_channel}>' if log_channel else 'Not set'

        embed.add_field(
            name="General Settings",
            value=f"**Enabled:** {'Yes' if settings['enabled'] else 'No'}\n"
                  f"**Log Channel:** {log_channel_text}",
            inline=False
        )

        # Module settings with thresholds
        modules_info = []
        module_data = [
            ('kick', 'Member Kick', settings.get('kick_enabled'), settings.get('kick_threshold'), settings.get('kick_punishment')),
            ('ban', 'Member Ban', settings.get('ban_enabled'), settings.get('ban_threshold'), settings.get('ban_punishment')),
            ('channel', 'Channel Actions', settings.get('channel_enabled'), settings.get('channel_threshold'), settings.get('channel_punishment')),
            ('role', 'Role Delete', settings.get('role_enabled'), settings.get('role_threshold'), settings.get('role_punishment')),
            ('webhook', 'Webhook Creation', settings.get('webhook_enabled'), settings.get('webhook_threshold'), settings.get('webhook_punishment')),
            ('emoji', 'Emoji Delete', settings.get('emoji_enabled'), settings.get('emoji_threshold'), settings.get('emoji_punishment'))
        ]

        for module, name, enabled, threshold, punishment in module_data:
            status = "ON" if enabled else "OFF"
            modules_info.append(f"{status} **{name}**\n   Threshold: {threshold}/min | Punishment: {punishment}")
        
        embed.add_field(
            name="Protection Modules",
            value="\n".join(modules_info[:3]),
            inline=True
        )
        embed.add_field(
            name="‎",
            value="\n".join(modules_info[3:]),
            inline=True
        )
        
        # Special modules
        special_modules = []
        if settings.get('vanity_enabled'):
            special_modules.append(f"ON **Vanity URL Protection**\n   Punishment: {settings.get('vanity_punishment')}")
        else:
            special_modules.append("OFF **Vanity URL Protection**")

        if settings.get('botadd_enabled'):
            special_modules.append(f"ON **Bot Addition Protection**\n   Punishment: {settings.get('botadd_punishment')}")
        else:
            special_modules.append("OFF **Bot Addition Protection**")

        if settings.get('permissions_enabled'):
            special_modules.append(f"ON **Dangerous Permissions**\n   Punishment: {settings.get('permissions_punishment')}")
        else:
            special_modules.append("OFF **Dangerous Permissions**")
        
        embed.add_field(
            name="Special Protection",
            value="\n".join(special_modules),
            inline=False
        )
        
        await ctx.send(embed=embed)

    @antinuke.command(brief="Prevent mass member kick")
    @checks.guild_only()
    @checks.bot_has_perms(kick_members=True)
    async def kick(self, ctx, status: converters.BoolConverter = None, threshold: int = 5, punishment: str = "kick"):
        """
        Configure protection against mass member kicks.

        Parameters:
        - status: enable/disable (true/false, on/off, yes/no)
        - threshold: number of kicks per minute before triggering (default: 5)
        - punishment: kick, ban, or strip_roles (default: kick)
        """
        if not self.is_owner_or_admin(ctx):
            return await ctx.fail("Only the server owner can configure antinuke settings.")

        settings = await self.get_antinuke_settings(ctx.guild.id)

        if status is None:
            # Show current settings
            embed = discord.Embed(
                title="Member Kick Protection",
                color=0x2596BE if settings['kick_enabled'] else 0x808080
            )
            embed.add_field(name="Status", value="Enabled" if settings['kick_enabled'] else "Disabled", inline=True)
            embed.add_field(name="Threshold", value=f"{settings['kick_threshold']}/minute", inline=True)
            embed.add_field(name="Punishment", value=settings['kick_punishment'].title(), inline=True)
            return await ctx.send(embed=embed)

        # Validate punishment
        valid_punishments = ['kick', 'ban', 'strip_roles']
        if punishment.lower() not in valid_punishments:
            return await ctx.fail(f"Invalid punishment. Valid options: {', '.join(valid_punishments)}")

        # Update settings
        query = """
                UPDATE antinuke
                SET kick_enabled = $1, kick_threshold = $2, kick_punishment = $3
                WHERE server_id = $4;
                """
        await self.bot.cxn.execute(query, status, threshold, punishment.lower(), ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        status_text = "enabled" if status else "disabled"
        await ctx.success(f"Member kick protection {status_text}. Threshold: {threshold}/min, Punishment: {punishment}")

    @antinuke.command(brief="Prevent mass member ban")
    @checks.guild_only()
    @checks.bot_has_perms(ban_members=True)
    async def ban(self, ctx, status: converters.BoolConverter = None, threshold: int = 3, punishment: str = "ban"):
        """
        Configure protection against mass member bans.

        Parameters:
        - status: enable/disable (true/false, on/off, yes/no)
        - threshold: number of bans per minute before triggering (default: 3)
        - punishment: kick, ban, or strip_roles (default: ban)
        """
        if not self.is_owner_or_admin(ctx):
            return await ctx.fail("Only the server owner can configure antinuke settings.")

        settings = await self.get_antinuke_settings(ctx.guild.id)

        if status is None:
            # Show current settings
            embed = discord.Embed(
                title="Member Ban Protection",
                color=0x2596BE if settings['ban_enabled'] else 0x808080
            )
            embed.add_field(name="Status", value="Enabled" if settings['ban_enabled'] else "Disabled", inline=True)
            embed.add_field(name="Threshold", value=f"{settings['ban_threshold']}/minute", inline=True)
            embed.add_field(name="Punishment", value=settings['ban_punishment'].title(), inline=True)
            return await ctx.send(embed=embed)

        # Validate punishment
        valid_punishments = ['kick', 'ban', 'strip_roles']
        if punishment.lower() not in valid_punishments:
            return await ctx.fail(f"Invalid punishment. Valid options: {', '.join(valid_punishments)}")

        # Update settings
        query = """
                UPDATE antinuke
                SET ban_enabled = $1, ban_threshold = $2, ban_punishment = $3
                WHERE server_id = $4;
                """
        await self.bot.cxn.execute(query, status, threshold, punishment.lower(), ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        status_text = "enabled" if status else "disabled"
        await ctx.success(f"Member ban protection {status_text}. Threshold: {threshold}/min, Punishment: {punishment}")

    @antinuke.command(brief="Prevent mass channel create and delete")
    @checks.guild_only()
    @checks.bot_has_perms(manage_channels=True)
    async def channel(self, ctx, status: converters.BoolConverter = None, threshold: int = 5, punishment: str = "kick"):
        """
        Configure protection against mass channel creation/deletion.

        Parameters:
        - status: enable/disable (true/false, on/off, yes/no)
        - threshold: number of channel actions per minute before triggering (default: 5)
        - punishment: kick, ban, or strip_roles (default: kick)
        """
        if not self.is_owner_or_admin(ctx):
            return await ctx.fail("Only the server owner can configure antinuke settings.")

        settings = await self.get_antinuke_settings(ctx.guild.id)

        if status is None:
            # Show current settings
            embed = discord.Embed(
                title="Channel Protection",
                color=0x2596BE if settings['channel_enabled'] else 0x808080
            )
            embed.add_field(name="Status", value="Enabled" if settings['channel_enabled'] else "Disabled", inline=True)
            embed.add_field(name="Threshold", value=f"{settings['channel_threshold']}/minute", inline=True)
            embed.add_field(name="Punishment", value=settings['channel_punishment'].title(), inline=True)
            return await ctx.send(embed=embed)

        # Validate punishment
        valid_punishments = ['kick', 'ban', 'strip_roles']
        if punishment.lower() not in valid_punishments:
            return await ctx.fail(f"Invalid punishment. Valid options: {', '.join(valid_punishments)}")

        # Update settings
        query = """
                UPDATE antinuke
                SET channel_enabled = $1, channel_threshold = $2, channel_punishment = $3
                WHERE server_id = $4;
                """
        await self.bot.cxn.execute(query, status, threshold, punishment.lower(), ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        status_text = "enabled" if status else "disabled"
        await ctx.success(f"Channel protection {status_text}. Threshold: {threshold}/min, Punishment: {punishment}")

    @antinuke.command(brief="Prevent mass role delete")
    @checks.guild_only()
    @checks.bot_has_perms(manage_roles=True)
    async def role(self, ctx, status: converters.BoolConverter = None, threshold: int = 5, punishment: str = "kick"):
        """
        Configure protection against mass role deletion.

        Parameters:
        - status: enable/disable (true/false, on/off, yes/no)
        - threshold: number of role deletions per minute before triggering (default: 5)
        - punishment: kick, ban, or strip_roles (default: kick)
        """
        if not self.is_owner_or_admin(ctx):
            return await ctx.fail("Only the server owner can configure antinuke settings.")

        settings = await self.get_antinuke_settings(ctx.guild.id)

        if status is None:
            # Show current settings
            embed = discord.Embed(
                title="Role Protection",
                color=0x2596BE if settings['role_enabled'] else 0x808080
            )
            embed.add_field(name="Status", value="Enabled" if settings['role_enabled'] else "Disabled", inline=True)
            embed.add_field(name="Threshold", value=f"{settings['role_threshold']}/minute", inline=True)
            embed.add_field(name="Punishment", value=settings['role_punishment'].title(), inline=True)
            return await ctx.send(embed=embed)

        # Validate punishment
        valid_punishments = ['kick', 'ban', 'strip_roles']
        if punishment.lower() not in valid_punishments:
            return await ctx.fail(f"Invalid punishment. Valid options: {', '.join(valid_punishments)}")

        # Update settings
        query = """
                UPDATE antinuke
                SET role_enabled = $1, role_threshold = $2, role_punishment = $3
                WHERE server_id = $4;
                """
        await self.bot.cxn.execute(query, status, threshold, punishment.lower(), ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        status_text = "enabled" if status else "disabled"
        await ctx.success(f"Role protection {status_text}. Threshold: {threshold}/min, Punishment: {punishment}")

    @antinuke.command(brief="Prevent mass webhook creation")
    @checks.guild_only()
    @checks.bot_has_perms(manage_webhooks=True)
    async def webhook(self, ctx, status: converters.BoolConverter = None, threshold: int = 3, punishment: str = "ban"):
        """
        Configure protection against mass webhook creation.

        Parameters:
        - status: enable/disable (true/false, on/off, yes/no)
        - threshold: number of webhooks per minute before triggering (default: 3)
        - punishment: kick, ban, or strip_roles (default: ban)
        """
        if not self.is_owner_or_admin(ctx):
            return await ctx.fail("Only the server owner can configure antinuke settings.")

        settings = await self.get_antinuke_settings(ctx.guild.id)

        if status is None:
            # Show current settings
            embed = discord.Embed(
                title="Webhook Protection",
                color=0x2596BE if settings['webhook_enabled'] else 0x808080
            )
            embed.add_field(name="Status", value="Enabled" if settings['webhook_enabled'] else "Disabled", inline=True)
            embed.add_field(name="Threshold", value=f"{settings['webhook_threshold']}/minute", inline=True)
            embed.add_field(name="Punishment", value=settings['webhook_punishment'].title(), inline=True)
            return await ctx.send(embed=embed)

        # Validate punishment
        valid_punishments = ['kick', 'ban', 'strip_roles']
        if punishment.lower() not in valid_punishments:
            return await ctx.fail(f"Invalid punishment. Valid options: {', '.join(valid_punishments)}")

        # Update settings
        query = """
                UPDATE antinuke
                SET webhook_enabled = $1, webhook_threshold = $2, webhook_punishment = $3
                WHERE server_id = $4;
                """
        await self.bot.cxn.execute(query, status, threshold, punishment.lower(), ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        status_text = "enabled" if status else "disabled"
        await ctx.success(f"Webhook protection {status_text}. Threshold: {threshold}/min, Punishment: {punishment}")

    @antinuke.command(brief="Prevent mass emoji delete Warning: This module may be unstable due to Discords rate limit")
    @checks.guild_only()
    @checks.bot_has_perms(manage_emojis=True)
    async def emoji(self, ctx, status: converters.BoolConverter = None, threshold: int = 10, punishment: str = "kick"):
        """
        Configure protection against mass emoji deletion.

        Parameters:
        - status: enable/disable (true/false, on/off, yes/no)
        - threshold: number of emoji deletions per minute before triggering (default: 10)
        - punishment: kick, ban, or strip_roles (default: kick)

        Warning: This module may be unstable due to Discord's rate limits.
        """
        if not self.is_owner_or_admin(ctx):
            return await ctx.fail("Only the server owner can configure antinuke settings.")

        settings = await self.get_antinuke_settings(ctx.guild.id)

        if status is None:
            # Show current settings
            embed = discord.Embed(
                title="Emoji Protection",
                color=0x2596BE if settings['emoji_enabled'] else 0x808080
            )
            embed.add_field(name="Status", value="Enabled" if settings['emoji_enabled'] else "Disabled", inline=True)
            embed.add_field(name="Threshold", value=f"{settings['emoji_threshold']}/minute", inline=True)
            embed.add_field(name="Punishment", value=settings['emoji_punishment'].title(), inline=True)
            embed.add_field(name="Warning", value="May be unstable due to Discord rate limits", inline=False)
            return await ctx.send(embed=embed)

        # Validate punishment
        valid_punishments = ['kick', 'ban', 'strip_roles']
        if punishment.lower() not in valid_punishments:
            return await ctx.fail(f"Invalid punishment. Valid options: {', '.join(valid_punishments)}")

        # Update settings
        query = """
                UPDATE antinuke
                SET emoji_enabled = $1, emoji_threshold = $2, emoji_punishment = $3
                WHERE server_id = $4;
                """
        await self.bot.cxn.execute(query, status, threshold, punishment.lower(), ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        status_text = "enabled" if status else "disabled"
        await ctx.success(f"Emoji protection {status_text}. Threshold: {threshold}/min, Punishment: {punishment}")

    @antinuke.command(brief="Punish users that change the server vanity")
    @checks.guild_only()
    async def vanity(self, ctx, status: converters.BoolConverter = None, punishment: str = "ban"):
        """
        Configure protection against vanity URL changes.

        Parameters:
        - status: enable/disable (true/false, on/off, yes/no)
        - punishment: kick, ban, or strip_roles (default: ban)
        """
        if not self.is_owner_or_admin(ctx):
            return await ctx.fail("Only the server owner can configure antinuke settings.")

        # Check if server has vanity URL feature
        if "VANITY_URL" not in ctx.guild.features:
            return await ctx.fail("This server doesn't have access to vanity URLs.")

        settings = await self.get_antinuke_settings(ctx.guild.id)

        if status is None:
            # Show current settings
            embed = discord.Embed(
                title="Vanity URL Protection",
                color=0x2596BE if settings['vanity_enabled'] else 0x808080
            )
            embed.add_field(name="Status", value="Enabled" if settings['vanity_enabled'] else "Disabled", inline=True)
            embed.add_field(name="Punishment", value=settings['vanity_punishment'].title(), inline=True)
            embed.add_field(name="Current Vanity", value=ctx.guild.vanity_url_code or "None", inline=True)
            return await ctx.send(embed=embed)

        # Validate punishment
        valid_punishments = ['kick', 'ban', 'strip_roles']
        if punishment.lower() not in valid_punishments:
            return await ctx.fail(f"Invalid punishment. Valid options: {', '.join(valid_punishments)}")

        # Update settings
        query = """
                UPDATE antinuke
                SET vanity_enabled = $1, vanity_punishment = $2
                WHERE server_id = $3;
                """
        await self.bot.cxn.execute(query, status, punishment.lower(), ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        status_text = "enabled" if status else "disabled"
        await ctx.success(f"Vanity URL protection {status_text}. Punishment: {punishment}")

    @antinuke.command(brief="Prevent new bot additions")
    @checks.guild_only()
    async def botadd(self, ctx, status: converters.BoolConverter = None, punishment: str = "kick"):
        """
        Configure protection against unauthorized bot additions.

        Parameters:
        - status: enable/disable (true/false, on/off, yes/no)
        - punishment: kick, ban, or strip_roles (default: kick)
        """
        if not self.is_owner_or_admin(ctx):
            return await ctx.fail("Only the server owner can configure antinuke settings.")

        settings = await self.get_antinuke_settings(ctx.guild.id)

        if status is None:
            # Show current settings
            embed = discord.Embed(
                title="Bot Addition Protection",
                color=0x2596BE if settings['botadd_enabled'] else 0x808080
            )
            embed.add_field(name="Status", value="Enabled" if settings['botadd_enabled'] else "Disabled", inline=True)
            embed.add_field(name="Punishment", value=settings['botadd_punishment'].title(), inline=True)
            embed.add_field(name="Whitelisted Bots", value=str(len(settings.get('whitelisted_bots', []))), inline=True)
            return await ctx.send(embed=embed)

        # Validate punishment
        valid_punishments = ['kick', 'ban', 'strip_roles']
        if punishment.lower() not in valid_punishments:
            return await ctx.fail(f"Invalid punishment. Valid options: {', '.join(valid_punishments)}")

        # Update settings
        query = """
                UPDATE antinuke
                SET botadd_enabled = $1, botadd_punishment = $2
                WHERE server_id = $3;
                """
        await self.bot.cxn.execute(query, status, punishment.lower(), ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        status_text = "enabled" if status else "disabled"
        await ctx.success(f"Bot addition protection {status_text}. Punishment: {punishment}")

    @antinuke.command(brief="Watch dangerous permissions being granted or removed")
    @checks.guild_only()
    @checks.bot_has_perms(manage_roles=True)
    async def permissions(self, ctx, typee: str = None, permission: str = None, *, flags: str = None):
        """
        Configure protection against dangerous permission changes.

        Parameters:
        - type: grant or remove
        - permission: administrator, manage_guild, manage_roles, etc.
        - flags: Additional settings
        """
        if not self.is_owner_or_admin(ctx):
            return await ctx.fail("Only the server owner can configure antinuke settings.")

        settings = await self.get_antinuke_settings(ctx.guild.id)

        dangerous_perms = [
            'administrator', 'manage_guild', 'manage_roles', 'manage_channels',
            'manage_messages', 'manage_webhooks', 'manage_emojis', 'ban_members',
            'kick_members', 'manage_nicknames', 'mute_members', 'deafen_members'
        ]

        if typee is None:
            # Show current settings
            embed = discord.Embed(
                title="Dangerous Permissions Protection",
                color=0x2596BE if settings['permissions_enabled'] else 0x808080
            )
            embed.add_field(name="Status", value="Enabled" if settings['permissions_enabled'] else "Disabled", inline=True)
            embed.add_field(name="Punishment", value=settings['permissions_punishment'].title(), inline=True)
            embed.add_field(
                name="Monitored Permissions",
                value=", ".join(dangerous_perms[:6]) + "\n" + ", ".join(dangerous_perms[6:]),
                inline=False
            )
            return await ctx.send(embed=embed)

        if typee.lower() not in ['grant', 'remove']:
            return await ctx.fail("Type must be 'grant' or 'remove'.")

        if permission and permission.lower() not in dangerous_perms:
            return await ctx.fail(f"Permission must be one of: {', '.join(dangerous_perms)}")

        # For now, just enable/disable the general protection
        # In a full implementation, you'd store specific permission configurations
        query = """
                UPDATE antinuke
                SET permissions_enabled = $1
                WHERE server_id = $2;
                """
        await self.bot.cxn.execute(query, True, ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        await ctx.success(f"Dangerous permissions protection enabled for {typee} {permission or 'all permissions'}.")

    @antinuke.command(brief="Give a user permissions to edit antinuke settings")
    @checks.guild_only()
    async def admin(self, ctx, member: converters.DiscordMember):
        """
        Add or remove a user as an antinuke admin.

        Antinuke admins can configure settings but cannot add/remove other admins.
        """
        if not self.is_owner_or_admin(ctx):
            return await ctx.fail("Only the server owner can manage antinuke admins.")

        if member.id == ctx.guild.owner_id:
            return await ctx.fail("The server owner is already an antinuke admin.")

        if member.bot:
            return await ctx.fail("Bots cannot be antinuke admins.")

        settings = await self.get_antinuke_settings(ctx.guild.id)
        admin_users = settings.get('admin_users', [])

        if member.id in admin_users:
            # Remove admin
            admin_users.remove(member.id)
            action = "removed from"
        else:
            # Add admin
            admin_users.append(member.id)
            action = "added to"

        query = """
                UPDATE antinuke
                SET admin_users = $1
                WHERE server_id = $2;
                """
        await self.bot.cxn.execute(query, admin_users, ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        await ctx.success(f"{member.mention} has been {action} antinuke admins.")

    @antinuke.command(brief="View all antinuke admins")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def admins(self, ctx):
        """
        View all users who can configure antinuke settings.
        """
        settings = await self.get_antinuke_settings(ctx.guild.id)
        admin_users = settings.get('admin_users', [])

        embed = discord.Embed(
            title="Antinuke Admins",
            color=0x2596BE
        )

        # Server owner
        owner = ctx.guild.owner
        embed.add_field(
            name="Server Owner",
            value=f"{owner.mention} ({owner.display_name})",
            inline=False
        )

        # Antinuke admins
        if admin_users:
            admin_mentions = []
            for user_id in admin_users:
                user = ctx.guild.get_member(user_id)
                if user:
                    admin_mentions.append(f"{user.mention} ({user.display_name})")
                else:
                    admin_mentions.append(f"<@{user_id}> (User not found)")

            embed.add_field(
                name=f"Antinuke Admins ({len(admin_users)})",
                value="\n".join(admin_mentions),
                inline=False
            )
        else:
            embed.add_field(
                name="Antinuke Admins",
                value="No additional admins configured",
                inline=False
            )

        embed.set_footer(text=f"Use {ctx.prefix}antinuke admin <member> to add/remove admins")
        await ctx.send(embed=embed)

    @antinuke.command(brief="Whitelist a member from triggering antinuke or a bot to join")
    @checks.guild_only()
    async def whitelist(self, ctx, member: converters.DiscordUser):
        """
        Add or remove a user/bot from the antinuke whitelist.

        Whitelisted users won't trigger antinuke protections.
        Whitelisted bots can be added to the server even with bot protection enabled.
        """
        if not await self.is_antinuke_admin(ctx):
            return await ctx.fail("Only server owners and antinuke admins can manage the whitelist.")

        if member.id == ctx.guild.owner_id:
            return await ctx.fail("The server owner is automatically whitelisted.")

        settings = await self.get_antinuke_settings(ctx.guild.id)

        if member.bot:
            whitelist_key = 'whitelisted_bots'
            whitelist_type = "bot"
        else:
            whitelist_key = 'whitelisted_users'
            whitelist_type = "user"

        whitelist = settings.get(whitelist_key, [])

        if member.id in whitelist:
            # Remove from whitelist
            whitelist.remove(member.id)
            action = "removed from"
        else:
            # Add to whitelist
            whitelist.append(member.id)
            action = "added to"

        query = f"""
                UPDATE antinuke
                SET {whitelist_key} = $1
                WHERE server_id = $2;
                """
        await self.bot.cxn.execute(query, whitelist, ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        await ctx.success(f"{whitelist_type.title()} {member.mention} has been {action} the antinuke whitelist.")

    @antinuke.command(brief="View all enabled modules along with whitelisted members & bots")
    @checks.guild_only()
    async def list(self, ctx):
        """
        View all enabled antinuke modules and whitelisted users/bots.
        """
        if not self.is_owner_or_admin(ctx):
            return await ctx.fail("Only server owners can view the full antinuke list.")

        settings = await self.get_antinuke_settings(ctx.guild.id)

        embed = discord.Embed(
            title="Antinuke Status List",
            color=0x2596BE if settings['enabled'] else 0x808080
        )

        # Enabled modules
        enabled_modules = []
        module_checks = [
            ('kick_enabled', 'Member Kick', settings.get('kick_threshold')),
            ('ban_enabled', 'Member Ban', settings.get('ban_threshold')),
            ('channel_enabled', 'Channel Actions', settings.get('channel_threshold')),
            ('role_enabled', 'Role Delete', settings.get('role_threshold')),
            ('webhook_enabled', 'Webhook Creation', settings.get('webhook_threshold')),
            ('emoji_enabled', 'Emoji Delete', settings.get('emoji_threshold')),
            ('vanity_enabled', 'Vanity URL', None),
            ('botadd_enabled', 'Bot Addition', None),
            ('permissions_enabled', 'Dangerous Permissions', None)
        ]

        for setting, name, threshold in module_checks:
            if settings.get(setting, False):
                if threshold:
                    enabled_modules.append(f"+ {name} ({threshold}/min)")
                else:
                    enabled_modules.append(f"+ {name}")

        if enabled_modules:
            embed.add_field(
                name="Enabled Modules",
                value="\n".join(enabled_modules),
                inline=False
            )
        else:
            embed.add_field(
                name="Enabled Modules",
                value="No modules enabled",
                inline=False
            )

        # Whitelisted users
        whitelisted_users = settings.get('whitelisted_users', [])
        if whitelisted_users:
            user_mentions = []
            for user_id in whitelisted_users[:10]:  # Limit to 10 to avoid embed limits
                user = ctx.guild.get_member(user_id)
                if user:
                    user_mentions.append(f"{user.mention}")
                else:
                    user_mentions.append(f"<@{user_id}>")

            if len(whitelisted_users) > 10:
                user_mentions.append(f"... and {len(whitelisted_users) - 10} more")

            embed.add_field(
                name=f"Whitelisted Users ({len(whitelisted_users)})",
                value="\n".join(user_mentions),
                inline=True
            )
        else:
            embed.add_field(
                name="Whitelisted Users",
                value="None",
                inline=True
            )

        # Whitelisted bots
        whitelisted_bots = settings.get('whitelisted_bots', [])
        if whitelisted_bots:
            bot_mentions = []
            for bot_id in whitelisted_bots[:10]:  # Limit to 10
                bot = ctx.guild.get_member(bot_id)
                if bot:
                    bot_mentions.append(f"{bot.mention}")
                else:
                    bot_mentions.append(f"<@{bot_id}>")

            if len(whitelisted_bots) > 10:
                bot_mentions.append(f"... and {len(whitelisted_bots) - 10} more")

            embed.add_field(
                name=f"Whitelisted Bots ({len(whitelisted_bots)})",
                value="\n".join(bot_mentions),
                inline=True
            )
        else:
            embed.add_field(
                name="Whitelisted Bots",
                value="None",
                inline=True
            )

        await ctx.send(embed=embed)

    # Event listeners for monitoring actions

    @commands.Cog.listener()
    async def on_member_remove(self, member):
        """Monitor member kicks"""
        if not member.guild:
            return

        settings = await self.get_antinuke_settings(member.guild.id)
        if not settings.get('enabled') or not settings.get('kick_enabled'):
            return

        # Get audit log to find who kicked the member
        try:
            async for entry in member.guild.audit_logs(action=discord.AuditLogAction.kick, limit=1):
                if entry.target.id == member.id:
                    kicker = entry.user

                    # Skip if whitelisted or bot owner
                    if (self.is_whitelisted(settings, kicker.id) or
                        kicker.id == member.guild.owner_id or
                        kicker.id in self.bot.config.OWNERS):
                        return

                    # Check rate limit
                    if await self.check_rate_limit(
                        member.guild.id, kicker.id, 'kick', settings['kick_threshold']
                    ):
                        # Apply punishment
                        reason = f"Antinuke: Exceeded kick threshold ({settings['kick_threshold']}/min)"
                        success = await self.punish_user(
                            member.guild, kicker, settings['kick_punishment'], reason
                        )

                        if success:
                            await self.log_action(
                                member.guild.id, kicker.id, 'kick_protection',
                                settings['kick_punishment'],
                                {'kicked_user': member.id, 'reason': reason}
                            )
                    break
        except discord.Forbidden:
            pass  # No audit log access

    @commands.Cog.listener()
    async def on_member_ban(self, guild, user):
        """Monitor member bans"""
        settings = await self.get_antinuke_settings(guild.id)
        if not settings.get('enabled') or not settings.get('ban_enabled'):
            return

        # Get audit log to find who banned the member
        try:
            async for entry in guild.audit_logs(action=discord.AuditLogAction.ban, limit=1):
                if entry.target.id == user.id:
                    banner = entry.user

                    # Skip if whitelisted or bot owner
                    if (self.is_whitelisted(settings, banner.id) or
                        banner.id == guild.owner_id or
                        banner.id in self.bot.config.OWNERS):
                        return

                    # Check rate limit
                    if await self.check_rate_limit(
                        guild.id, banner.id, 'ban', settings['ban_threshold']
                    ):
                        # Apply punishment
                        reason = f"Antinuke: Exceeded ban threshold ({settings['ban_threshold']}/min)"
                        success = await self.punish_user(
                            guild, banner, settings['ban_punishment'], reason
                        )

                        if success:
                            await self.log_action(
                                guild.id, banner.id, 'ban_protection',
                                settings['ban_punishment'],
                                {'banned_user': user.id, 'reason': reason}
                            )
                    break
        except discord.Forbidden:
            pass  # No audit log access

    @commands.Cog.listener()
    async def on_guild_channel_create(self, channel):
        """Monitor channel creation"""
        await self._handle_channel_action(channel.guild, 'channel_create')

    @commands.Cog.listener()
    async def on_guild_channel_delete(self, channel):
        """Monitor channel deletion"""
        await self._handle_channel_action(channel.guild, 'channel_delete')

    async def _handle_channel_action(self, guild, action_type):
        """Handle channel create/delete actions"""
        settings = await self.get_antinuke_settings(guild.id)
        if not settings.get('enabled') or not settings.get('channel_enabled'):
            return

        audit_action = (discord.AuditLogAction.channel_create if action_type == 'channel_create'
                       else discord.AuditLogAction.channel_delete)

        try:
            async for entry in guild.audit_logs(action=audit_action, limit=5):
                actor = entry.user
                # Only punish if not whitelisted and not owner
                if (self.is_whitelisted(settings, actor.id) or
                    actor.id == guild.owner_id or
                    actor.id in self.bot.config.OWNERS):
                    continue

                # Track actions in real-time
                if await self.check_rate_limit(
                    guild.id, actor.id, 'channel', settings['channel_threshold']
                ):
                    reason = f"Antinuke: Exceeded channel action threshold ({settings['channel_threshold']}/min)"
                    success = await self.punish_user(
                        guild, actor, settings['channel_punishment'], reason
                    )
                    if success:
                        await self.log_action(
                            guild.id, actor.id, 'channel_protection',
                            settings['channel_punishment'],
                            {'action': action_type, 'reason': reason}
                        )
                    break
        except discord.Forbidden:
            pass  # No audit log access

    @commands.Cog.listener()
    async def on_guild_role_delete(self, role):
        """Monitor role deletion"""
        settings = await self.get_antinuke_settings(role.guild.id)
        if not settings.get('enabled') or not settings.get('role_enabled'):
            return

        # Get audit log to find who deleted the role
        try:
            async for entry in role.guild.audit_logs(action=discord.AuditLogAction.role_delete, limit=1):
                if entry.target.id == role.id:
                    deleter = entry.user

                    # Skip if whitelisted or bot owner
                    if (self.is_whitelisted(settings, deleter.id) or
                        deleter.id == role.guild.owner_id or
                        deleter.id in self.bot.config.OWNERS):
                        return

                    # Check rate limit
                    if await self.check_rate_limit(
                        role.guild.id, deleter.id, 'role', settings['role_threshold']
                    ):
                        # Apply punishment
                        reason = f"Antinuke: Exceeded role deletion threshold ({settings['role_threshold']}/min)"
                        success = await self.punish_user(
                            role.guild, deleter, settings['role_punishment'], reason
                        )

                        if success:
                            await self.log_action(
                                role.guild.id, deleter.id, 'role_protection',
                                settings['role_punishment'],
                                {'deleted_role': role.name, 'reason': reason}
                            )
                    break
        except discord.Forbidden:
            pass  # No audit log access

    @commands.Cog.listener()
    async def on_webhooks_update(self, channel):
        """Monitor webhook creation"""
        settings = await self.get_antinuke_settings(channel.guild.id)
        if not settings.get('enabled') or not settings.get('webhook_enabled'):
            return

        # Get audit log to find who created the webhook
        try:
            async for entry in channel.guild.audit_logs(action=discord.AuditLogAction.webhook_create, limit=1):
                creator = entry.user

                # Skip if whitelisted or bot owner
                if (self.is_whitelisted(settings, creator.id) or
                    creator.id == channel.guild.owner_id or
                    creator.id in self.bot.config.OWNERS):
                    return

                # Check rate limit
                if await self.check_rate_limit(
                    channel.guild.id, creator.id, 'webhook', settings['webhook_threshold']
                ):
                    # Apply punishment
                    reason = f"Antinuke: Exceeded webhook creation threshold ({settings['webhook_threshold']}/min)"
                    success = await self.punish_user(
                        channel.guild, creator, settings['webhook_punishment'], reason
                    )

                    if success:
                        await self.log_action(
                            channel.guild.id, creator.id, 'webhook_protection',
                            settings['webhook_punishment'],
                            {'channel': channel.id, 'reason': reason}
                        )
                break
        except discord.Forbidden:
            pass  # No audit log access

    @commands.Cog.listener()
    async def on_guild_emojis_update(self, guild, before, after):
        """Monitor emoji deletion"""
        settings = await self.get_antinuke_settings(guild.id)
        if not settings.get('enabled') or not settings.get('emoji_enabled'):
            return

        # Check if emojis were deleted
        if len(before) > len(after):
            # Get audit log to find who deleted the emoji
            try:
                async for entry in guild.audit_logs(action=discord.AuditLogAction.emoji_delete, limit=1):
                    deleter = entry.user

                    # Skip if whitelisted or bot owner
                    if (self.is_whitelisted(settings, deleter.id) or
                        deleter.id == guild.owner_id or
                        deleter.id in self.bot.config.OWNERS):
                        return

                    # Check rate limit
                    if await self.check_rate_limit(
                        guild.id, deleter.id, 'emoji', settings['emoji_threshold']
                    ):
                        # Apply punishment
                        reason = f"Antinuke: Exceeded emoji deletion threshold ({settings['emoji_threshold']}/min)"
                        success = await self.punish_user(
                            guild, deleter, settings['emoji_punishment'], reason
                        )

                        if success:
                            await self.log_action(
                                guild.id, deleter.id, 'emoji_protection',
                                settings['emoji_punishment'],
                                {'deleted_count': len(before) - len(after), 'reason': reason}
                            )
                    break
            except discord.Forbidden:
                pass  # No audit log access

    @commands.Cog.listener()
    async def on_guild_update(self, before, after):
        """Monitor vanity URL changes"""
        if before.vanity_url_code != after.vanity_url_code:
            settings = await self.get_antinuke_settings(after.id)
            if not settings.get('enabled') or not settings.get('vanity_enabled'):
                return

            # Get audit log to find who changed the vanity
            try:
                async for entry in after.audit_logs(action=discord.AuditLogAction.guild_update, limit=1):
                    changer = entry.user

                    # Skip if whitelisted or bot owner
                    if (self.is_whitelisted(settings, changer.id) or
                        changer.id == after.owner_id or
                        changer.id in self.bot.config.OWNERS):
                        return

                    # Apply punishment immediately (no rate limit for vanity changes)
                    reason = f"Antinuke: Unauthorized vanity URL change"
                    success = await self.punish_user(
                        after, changer, settings['vanity_punishment'], reason
                    )

                    if success:
                        await self.log_action(
                            after.id, changer.id, 'vanity_protection',
                            settings['vanity_punishment'],
                            {
                                'old_vanity': before.vanity_url_code,
                                'new_vanity': after.vanity_url_code,
                                'reason': reason
                            }
                        )
                    break
            except discord.Forbidden:
                pass  # No audit log access

    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Monitor bot additions"""
        if not member.bot:
            return

        settings = await self.get_antinuke_settings(member.guild.id)
        if not settings.get('enabled') or not settings.get('botadd_enabled'):
            return

        # Check if bot is whitelisted
        if self.is_whitelisted(settings, member.id):
            return

        # Get audit log to find who added the bot
        try:
            async for entry in member.guild.audit_logs(action=discord.AuditLogAction.bot_add, limit=1):
                if entry.target.id == member.id:
                    adder = entry.user

                    # Skip if whitelisted or bot owner
                    if (self.is_whitelisted(settings, adder.id) or
                        adder.id == member.guild.owner_id or
                        adder.id in self.bot.config.OWNERS):
                        return

                    # Apply punishment immediately and kick the bot
                    reason = f"Antinuke: Unauthorized bot addition"

                    # Kick the bot first
                    try:
                        await member.guild.kick(member, reason=reason)
                    except:
                        pass

                    # Punish the user who added it
                    success = await self.punish_user(
                        member.guild, adder, settings['botadd_punishment'], reason
                    )

                    if success:
                        await self.log_action(
                            member.guild.id, adder.id, 'botadd_protection',
                            settings['botadd_punishment'],
                            {'bot_id': member.id, 'bot_name': str(member), 'reason': reason}
                        )
                    break
        except discord.Forbidden:
            pass  # No audit log access

    @commands.Cog.listener()
    async def on_member_update(self, before, after):
        """Monitor dangerous permission changes"""
        if before.roles == after.roles:
            return

        settings = await self.get_antinuke_settings(after.guild.id)
        if not settings.get('enabled') or not settings.get('permissions_enabled'):
            return

        # Check for dangerous permission changes
        dangerous_perms = [
            'administrator', 'manage_guild', 'manage_roles', 'manage_channels',
            'manage_messages', 'manage_webhooks', 'manage_emojis', 'ban_members',
            'kick_members', 'manage_nicknames', 'mute_members', 'deafen_members'
        ]

        # Get permissions before and after
        before_perms = before.guild_permissions
        after_perms = after.guild_permissions

        # Check if dangerous permissions were granted
        dangerous_granted = []
        for perm in dangerous_perms:
            if not getattr(before_perms, perm, False) and getattr(after_perms, perm, False):
                dangerous_granted.append(perm)

        if not dangerous_granted:
            return

        # Get audit log to find who granted the permissions
        try:
            async for entry in after.guild.audit_logs(action=discord.AuditLogAction.member_role_update, limit=5):
                if entry.target.id == after.id:
                    granter = entry.user

                    # Skip if whitelisted or bot owner
                    if (self.is_whitelisted(settings, granter.id) or
                        granter.id == after.guild.owner_id or
                        granter.id in self.bot.config.OWNERS):
                        return

                    # Apply punishment
                    reason = f"Antinuke: Granted dangerous permissions ({', '.join(dangerous_granted)})"
                    success = await self.punish_user(
                        after.guild, granter, settings['permissions_punishment'], reason
                    )

                    if success:
                        await self.log_action(
                            after.guild.id, granter.id, 'permissions_protection',
                            settings['permissions_punishment'],
                            {
                                'target_user': after.id,
                                'granted_permissions': dangerous_granted,
                                'reason': reason
                            }
                        )
                    break
        except discord.Forbidden:
            pass  # No audit log access

    async def send_log(self, guild_id, embed):
        """Send log message to configured log channel"""
        settings = await self.get_antinuke_settings(guild_id)
        log_channel_id = settings.get('log_channel')

        if not log_channel_id:
            return

        guild = self.bot.get_guild(guild_id)
        if not guild:
            return

        log_channel = guild.get_channel(log_channel_id)
        if not log_channel:
            return

        try:
            await log_channel.send(embed=embed)
        except discord.Forbidden:
            pass  # No permissions to send to log channel

    @antinuke.command(brief="Enable all antinuke modules")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def enableall(self, ctx):
        """Enable all antinuke modules with default thresholds and punishments."""
        # Enable the main system!
        query = "UPDATE antinuke SET enabled = TRUE WHERE server_id = $1;"
        await self.bot.cxn.execute(query, ctx.guild.id)
        await self.update_settings_cache(ctx.guild.id)

        modules = [
            ("kick", True, 5, "ban"),
            ("ban", True, 3, "ban"),
            ("channel", True, 5, "kick"),
            ("role", True, 5, "kick"),
            ("webhook", True, 3, "ban"),
            ("emoji", True, 10, "kick"),
            ("vanity", True, "ban"),
            ("botadd", True, "kick"),
            ("permissions", True)
        ]
        for mod in modules:
            cmd = getattr(self, mod[0])
            if mod[0] == "permissions":
                await cmd(ctx, "grant", "administrator")
            elif mod[0] == "vanity" or mod[0] == "botadd":
                await cmd(ctx, mod[1], mod[2])
            else:
                await cmd(ctx, mod[1], mod[2], mod[3])
        await ctx.send("✅ All antinuke modules and main system enabled with default settings.")


    @antinuke.command(brief="Set all antinuke thresholds to 1 and punishment to ban")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def insta(self, ctx):
        """Set all antinuke thresholds to 1 and punishment to ban."""
        query = """
        UPDATE antinuke SET
            enabled = TRUE,
            kick_enabled = TRUE, kick_threshold = 1, kick_punishment = 'ban',
            ban_enabled = TRUE, ban_threshold = 1, ban_punishment = 'ban',
            channel_enabled = TRUE, channel_threshold = 1, channel_punishment = 'ban',
            role_enabled = TRUE, role_threshold = 1, role_punishment = 'ban',
            webhook_enabled = TRUE, webhook_threshold = 1, webhook_punishment = 'ban',
            emoji_enabled = TRUE, emoji_threshold = 1, emoji_punishment = 'ban',
            vanity_enabled = TRUE, vanity_punishment = 'ban',
            botadd_enabled = TRUE, botadd_punishment = 'ban',
            permissions_enabled = TRUE, permissions_punishment = 'ban'
        WHERE server_id = $1;
    """
        try:
            await self.bot.cxn.execute(query, ctx.guild.id)
            await self.update_settings_cache(ctx.guild.id)
            await ctx.send("✅ All antinuke modules set to threshold 1 and punishment ban.")
        except Exception as e:
            await ctx.send(f"❌ Error updating antinuke settings: {e}")