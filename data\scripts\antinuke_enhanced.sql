-- Enhanced Antinuke configuration table updates
-- Add new columns for enhanced protection

-- Add new protection modules
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS invite_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS server_update_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS role_create_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS overwrite_enabled BOOLEAN DEFAULT FALSE;

-- Add new thresholds
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS invite_threshold INTEGER DEFAULT 10;
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS server_update_threshold INTEGER DEFAULT 3;
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS role_create_threshold INTEGER DEFAULT 3;
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS overwrite_threshold INTEGER DEFAULT 5;

-- Add new punishment settings
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS invite_punishment TEXT DEFAULT 'kick';
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS server_update_punishment TEXT DEFAULT 'ban';
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS role_create_punishment TEXT DEFAULT 'ban';
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS overwrite_punishment TEXT DEFAULT 'kick';

-- Add protection level setting
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS protection_level TEXT DEFAULT 'medium';

-- Add emergency lockdown settings
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS emergency_lockdown BOOLEAN DEFAULT FALSE;
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS lockdown_reason TEXT;
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS lockdown_timestamp TIMESTAMP;

-- Add auto-punishment escalation
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS auto_escalation BOOLEAN DEFAULT TRUE;
ALTER TABLE antinuke ADD COLUMN IF NOT EXISTS escalation_threshold INTEGER DEFAULT 3;

-- Enhanced suspicious activity tracking table
CREATE TABLE IF NOT EXISTS antinuke_suspicious (
    id BIGSERIAL PRIMARY KEY,
    server_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    action_type TEXT NOT NULL,
    severity_score INTEGER DEFAULT 1,
    timestamp TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    details JSONB DEFAULT '{}'::jsonb
);

-- Index for suspicious activity tracking
CREATE INDEX IF NOT EXISTS antinuke_suspicious_server_user_idx ON antinuke_suspicious(server_id, user_id);
CREATE INDEX IF NOT EXISTS antinuke_suspicious_timestamp_idx ON antinuke_suspicious(timestamp);

-- Emergency lockdown events table
CREATE TABLE IF NOT EXISTS antinuke_lockdowns (
    id BIGSERIAL PRIMARY KEY,
    server_id BIGINT NOT NULL,
    triggered_by BIGINT NOT NULL,
    reason TEXT NOT NULL,
    started_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    ended_at TIMESTAMP,
    auto_triggered BOOLEAN DEFAULT FALSE,
    details JSONB DEFAULT '{}'::jsonb
);

-- Index for lockdown events
CREATE INDEX IF NOT EXISTS antinuke_lockdowns_server_idx ON antinuke_lockdowns(server_id);
CREATE INDEX IF NOT EXISTS antinuke_lockdowns_time_idx ON antinuke_lockdowns(started_at);

-- Mass action detection table
CREATE TABLE IF NOT EXISTS antinuke_mass_actions (
    id BIGSERIAL PRIMARY KEY,
    server_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    action_type TEXT NOT NULL,
    action_count INTEGER DEFAULT 1,
    time_window INTERVAL DEFAULT '1 minute',
    first_action TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    last_action TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    punishment_applied TEXT,
    details JSONB DEFAULT '{}'::jsonb
);

-- Index for mass action detection
CREATE INDEX IF NOT EXISTS antinuke_mass_actions_server_user_idx ON antinuke_mass_actions(server_id, user_id);
CREATE INDEX IF NOT EXISTS antinuke_mass_actions_time_idx ON antinuke_mass_actions(first_action, last_action);

-- Function to clean old tracking data (run periodically)
CREATE OR REPLACE FUNCTION cleanup_antinuke_data() RETURNS void AS $$
BEGIN
    -- Clean suspicious activity older than 7 days
    DELETE FROM antinuke_suspicious WHERE timestamp < NOW() - INTERVAL '7 days';
    
    -- Clean mass action records older than 24 hours
    DELETE FROM antinuke_mass_actions WHERE last_action < NOW() - INTERVAL '24 hours';
    
    -- Clean old logs older than 30 days
    DELETE FROM antinuke_logs WHERE timestamp < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Create a view for recent suspicious activity
CREATE OR REPLACE VIEW antinuke_recent_activity AS
SELECT 
    al.server_id,
    al.user_id,
    al.action_type,
    al.punishment,
    al.timestamp,
    al.details,
    COUNT(*) OVER (PARTITION BY al.server_id, al.user_id ORDER BY al.timestamp RANGE INTERVAL '1 hour' PRECEDING) as recent_violations
FROM antinuke_logs al
WHERE al.timestamp > NOW() - INTERVAL '24 hours'
ORDER BY al.timestamp DESC;

-- Create a view for server protection status
CREATE OR REPLACE VIEW antinuke_protection_status AS
SELECT 
    a.server_id,
    a.enabled,
    a.protection_level,
    a.emergency_lockdown,
    COUNT(CASE WHEN al.timestamp > NOW() - INTERVAL '1 hour' THEN 1 END) as violations_last_hour,
    COUNT(CASE WHEN al.timestamp > NOW() - INTERVAL '24 hours' THEN 1 END) as violations_last_day,
    MAX(al.timestamp) as last_violation
FROM antinuke a
LEFT JOIN antinuke_logs al ON a.server_id = al.server_id
GROUP BY a.server_id, a.enabled, a.protection_level, a.emergency_lockdown;
